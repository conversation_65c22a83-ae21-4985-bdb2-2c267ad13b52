import { defineStore } from 'pinia'
import { ref } from 'vue'
import cloneDeep from 'lodash/cloneDeep'
import { getRouters } from '@/api/auth'
import Layout from '@/layouts/index.vue'
import router, { defaultRouterList } from '@/router'
import { isHttp } from '@/utils/validate'

// 自动导入views文件夹下所有vue文件
const modules = import.meta.glob('../../views/**/*.vue')

export const usePermissionStore = defineStore('permission', () => {
  const whiteListRouters = ref([
    '/login',
    '/register',
    '/contact',
    '/403',
    '/500',
    '/404',
    '/test'
  ])
  const menus = ref([])
  const allMenus = ref([])

  async function generateRoutes() {
    try {
      console.log('🚀 开始生成动态路由...')

      // 向后端请求路由数据
      const res = await getRouters()
      console.log('📡 getRouters响应:', res.data)

      const asyncRouter = filterAsyncRouter(cloneDeep(res.data))
      console.log('🔧 过滤后的路由:', asyncRouter)

      menus.value = asyncRouter
      allMenus.value = defaultRouterList.concat(asyncRouter)

      // 根据后台路由数据生成可访问路由表
      asyncRouter.forEach((route) => {
        // Layout路由的path可以是空字符串，这是正常的
        if (route.path !== undefined && !isHttp(route.path)) {
          console.log('➕ 添加动态路由:', route.path, route.name)
          router.addRoute(route) // 动态添加可访问路由表
        }
      })

      // 打印所有已注册的路由
      const allRoutes = router.getRoutes()
      console.log('📋 所有已注册路由:', allRoutes.map(r => ({
        path: r.path,
        name: r.name,
        component: r.component?.name || 'unknown'
      })))

      console.log('✅ 动态路由生成完成')
    } catch (error) {
      console.error('❌ 生成路由失败:', error)
      throw error
    }
  }

  // 检查路径是否在白名单中
  const isWhiteList = (path) => {
    return whiteListRouters.value.some((pattern) => {
      if (pattern === path) return true
      if (pattern.includes('*')) {
        const regexPattern = pattern.replace(/\*/g, '.*')
        const regex = new RegExp(`^${regexPattern}$`)
        return regex.test(path)
      }
      return false
    })
  }

  // 清空路由
  const clearRoutes = () => {
    menus.value = []
    allMenus.value = [...defaultRouterList]
  }

  // 获取菜单树
  const getMenuTree = () => {
    return menus.value
  }

  return {
    menus,
    allMenus,
    whiteListRouters,
    generateRoutes,
    isWhiteList,
    clearRoutes,
    getMenuTree,
  }
})

/**
 * 遍历后台传来的路由字符串，转换为组件对象
 * @param routers 后台传来的路由字符串
 */
function filterAsyncRouter(routers) {
  // 如果后端返回的是扁平结构的菜单，需要包装在Layout中
  if (routers.length > 0 && !routers.some(r => r.component === 'Layout')) {
    // 创建Layout包装器
    const layoutRoute = {
      path: '',
      component: Layout,
      redirect: routers[0] ? componentToPath(routers[0].component) : '/home',
      children: []
    }

    // 处理每个菜单项
    routers.forEach((route) => {
      const processedRoute = processMenuRoute(route)
      if (processedRoute) {
        layoutRoute.children.push(processedRoute)
      }
    })

    return [layoutRoute]
  }

  // 如果已经有Layout结构，按原逻辑处理
  return routers.filter((route) => {
    const processedRoute = processMenuRoute(route)
    if (route.children?.length) {
      route.children = filterAsyncRouter(route.children)
    } else {
      delete route.children
      delete route.redirect
    }
    return true
  })
}

/**
 * 处理单个菜单路由
 */
function processMenuRoute(route) {
  // 处理路径：优先使用数据库中的path字段，否则根据component生成
  if (route.path) {
    // 确保path以斜杠开头
    if (!route.path.startsWith('/')) {
      route.path = '/' + route.path
    }
    console.log(`📍 使用数据库path字段: ${route.title} -> ${route.path}`)
  } else if (route.component) {
    // 根据component生成path
    route.path = componentToPath(route.component)
    console.log(`🔧 根据component生成path: ${route.title} -> ${route.path}`)
  }

  // 构建meta对象
  route.meta = {
    title: route.title,
    icon: route.icon,
    hidden: route.is_hidden === 1,
    cache: route.is_cache === 1
  }

  // 设置name（用于路由缓存）- 基于component字段生成小写开头的驼峰形式
  if (route.component) {
    // 从component字段生成路由名称
    // 例如: Index/index -> index, GetMailbox/index -> getMailbox
    let componentName = route.component.replace(/\/index$/, '') // 移除/index后缀
    componentName = componentName.replace(/\//g, '') // 移除斜杠
    // 将首字母转换为小写
    route.name = componentName.charAt(0).toLowerCase() + componentName.slice(1)
  }

  if (route.component) {
    // Layout组件特殊处理
    if (route.component?.toString() === 'Layout') {
      route.component = Layout
    } else {
      route.component = loadView(route.component)
    }
  }

  return route
}

/**
 * 根据组件路径生成路由路径
 * @param component 组件路径，如 'Home/index' 或 'GetMailbox/index'
 */
function componentToPath(component) {
  if (!component) return '/'

  // 移除 /index 后缀
  let cleanComponent = component.replace(/\/index$/, '')

  // 直接使用组件路径作为URL路径
  const path = '/' + cleanComponent

  return path
}

/**
 * 加载组件
 * @param view 组件名称，如 'Home/index'
 */
export const loadView = (view) => {
  // 构建完整的路径
  const fullPath = `../../views/${view}.vue`

  // 查找匹配的模块
  for (const path in modules) {
    if (path === fullPath) {
      return () => modules[path]()
    }
  }

  // 如果没找到，尝试其他可能的路径格式
  for (const path in modules) {
    const dir = path.split('views/')[1]?.split('.vue')[0]
    if (dir === view) {
      return () => modules[path]()
    }
  }

  console.error(`无法找到组件: ${view}`)
  return null
}


